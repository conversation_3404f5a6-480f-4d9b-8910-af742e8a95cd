// 遇到数字：累加为可能的多位数字。
// 遇到 [：将当前数字和 [ 入栈，并重置 num。
// 遇到 ]：弹出栈中内容直到遇到 [，然后将中间字符串重复指定次数再压入栈。
// 其他字符直接入栈。
function transform(s) {
  const stack = [];
  let num = 0;

  for (const char of s) {
    if (/\d/.test(char)) {
      // 多位数字支持
      num = num * 10 + parseInt(char, 10);
    } else if (char === '[') {
      stack.push(num);
      stack.push(char);
      num = 0;
    } else if (char === ']') {
      let str = '';
      while (stack.length && stack[stack.length - 1] !== '[') {
        str = stack.pop() + str;
      }
      stack.pop(); // 弹出 '['
      const count = stack.pop();
      stack.push(str.repeat(count));
    } else {
      stack.push(char);
    }
  }

  return stack.join('');

}

console.log(transform("2[m]2[t]"))
console.log(transform("2[m2[t]]"))
console.log(transform("2[m2[t]]2[n]"))