// 容器样式
.capsule-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f3f4f6;
  font-family: system-ui, -apple-system, sans-serif;
}

.animation-wrapper {
  position: relative;
}

// 图标容器样式
.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
}

// 图标样式
.icon {
  width: 48px;
  height: 48px;
  background-color: #3b82f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, opacity 0.3s ease;
  transform: scale(1);
  opacity: 1;

  // 图标消失动画
  &.icon-hide {
    transform: scale(0);
    opacity: 0;
  }
}

// 胶囊样式
.capsule {
  position: absolute;
  top: 0;
  left: 0;
  width: 48px;
  height: 48px;
  background-color: #3b82f6;
  border-radius: 50%;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transform-origin: right center;
  
  // 胶囊展开动画
  animation: expandCapsule 0.6s ease-in-out forwards;
}

// 胶囊文字样式
.capsule-text {
  color: white;
  font-weight: 500;
  font-size: 14px;
  white-space: nowrap;
  opacity: 0;
  transform: translateX(30px);
  
  // 文字滑入动画
  animation: slideInText 0.4s ease-out 0.4s forwards;
}

// 胶囊展开动画关键帧
@keyframes expandCapsule {
  0% {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    transform: translateX(0);
  }
  100% {
    width: 200px;
    height: 48px;
    border-radius: 24px;
    transform: translateX(-152px);
  }
}

// 文字滑入动画关键帧
@keyframes slideInText {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
