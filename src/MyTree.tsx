import React, { useState, useCallback } from "react";

type DataNode = {
  title: React.ReactNode;
  key: string;
  children?: DataNode[];
};

interface TreeNodeProps {
  node: DataNode;
  expandedKeys: string[];
  setExpandedKeys: React.Dispatch<React.SetStateAction<string[]>>;
  selectedKeys: string[];
  setSelectedKeys: React.Dispatch<React.SetStateAction<string[]>>;
  parentKeys?: string[];
  onNodeSelect?: (selectedPath: string[]) => void; // 新增节点选择回调
}

const TreeNode = ({
  node,
  expandedKeys,
  setExpandedKeys,
  selectedKeys,
  setSelectedKeys,
  parentKeys = [],
  onNodeSelect,
}: TreeNodeProps) => {
  const isExpanded = expandedKeys.includes(node.key);
  const isSelected = selectedKeys.includes(node.key);

  // 当前节点的完整路径（所有父节点 + 当前节点）
  const nodePath = [...parentKeys, node.key];

  const handleToggle = (e: React.MouseEvent) => {
    e.stopPropagation();

    if (node.children) {
      setExpandedKeys((prev) =>
        isExpanded
          ? prev.filter((key) => key !== node.key)
          : [...prev, node.key]
      );
    }
  };

  const handleSelect = useCallback(() => {
    // 设置选中状态
    setSelectedKeys(nodePath);

    // 通知父组件选择变化
    if (onNodeSelect) {
      onNodeSelect(nodePath);
    }
  }, [nodePath, setSelectedKeys, onNodeSelect]);

  const nodeStyle = {
    cursor: "pointer",
    backgroundColor: isSelected ? "#e6f7ff" : "transparent",
  };

  return (
    <div style={{ margin: "4px 0" }}>
      <div onClick={handleSelect} style={nodeStyle}>
        <span onClick={handleToggle} style={{ marginRight: 8 }}>
          {node.children && (isExpanded ? "▼" : "▶")}
        </span>
        {node.title}
      </div>

      {node.children && isExpanded && (
        <div style={{ marginLeft: "24px" }}>
          {node.children.map((childNode) => (
            <TreeNode
              key={childNode.key}
              node={childNode}
              expandedKeys={expandedKeys}
              setExpandedKeys={setExpandedKeys}
              selectedKeys={selectedKeys}
              setSelectedKeys={setSelectedKeys}
              parentKeys={nodePath}
              onNodeSelect={onNodeSelect}
            />
          ))}
        </div>
      )}
    </div>
  );
};

interface TreeProps {
  treeData: DataNode[];
  initialExpandedKeys?: string[];
  initialSelectedKeys?: string[];
  onSelect?: (selectedPath: string[]) => void; // 新增外部选择回调
}

export const Tree = ({
  treeData,
  initialExpandedKeys = [],
  initialSelectedKeys = [],
  onSelect,
}: TreeProps) => {
  const [expandedKeys, setExpandedKeys] = useState(initialExpandedKeys);
  const [selectedKeys, setSelectedKeys] = useState(initialSelectedKeys);

  // 处理节点选择事件
  const handleNodeSelect = useCallback(
    (selectedPath: string[]) => {
      setSelectedKeys(selectedPath);
      if (onSelect) {
        onSelect(selectedPath);
      }
    },
    [onSelect]
  );

  return (
    <div>
      {treeData.map((node) => (
        <TreeNode
          key={node.key}
          node={node}
          expandedKeys={expandedKeys}
          setExpandedKeys={setExpandedKeys}
          selectedKeys={selectedKeys}
          setSelectedKeys={setSelectedKeys}
          onNodeSelect={handleNodeSelect}
        />
      ))}
    </div>
  );
};
