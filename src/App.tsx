import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { MessageCircle } from "lucide-react";

const CapsuleAnimation = () => {
  const [showIcon, setShowIcon] = useState(true);
  const [showCapsule, setShowCapsule] = useState(false);

  useEffect(() => {
    // 2秒后隐藏图标并显示胶囊
    const timer = setTimeout(() => {
      setShowIcon(false);
      setShowCapsule(true);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const containerStyle: React.CSSProperties = {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    minHeight: "100vh",
    backgroundColor: "#f3f4f6",
    fontFamily: "system-ui, -apple-system, sans-serif",
  };

  const iconContainerStyle: React.CSSProperties = {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  };

  const iconStyle: React.CSSProperties = {
    width: "48px",
    height: "48px",
    backgroundColor: "#3b82f6",
    borderRadius: "50%",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    boxShadow:
      "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
  };

  const capsuleStyle: React.CSSProperties = {
    backgroundColor: "#3b82f6",
    boxShadow:
      "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    overflow: "hidden",
  };

  const textStyle: React.CSSProperties = {
    color: "white",
    fontWeight: "500",
    fontSize: "14px",
    whiteSpace: "nowrap",
  };

  return (
    <div style={containerStyle}>
      <div style={{ position: "relative" }}>
        {/* 容器始终存在，只控制内容的显示 */}
        <motion.div
          style={{
            ...iconContainerStyle,
            width: "48px",
            height: "48px",
          }}
        >
          {/* 图标 - 只控制图标本身的缩放和透明度 */}
          <motion.div
            style={iconStyle}
            animate={{
              scale: showIcon ? 1 : 0,
              opacity: showIcon ? 1 : 0,
            }}
            transition={{ duration: 0.3 }}
          >
            <MessageCircle size={24} color="white" />
          </motion.div>
        </motion.div>

        {/* 胶囊动画 */}
        {showCapsule && (
          <motion.div
            initial={{
              width: 48,
              height: 48,
              borderRadius: "50%",
              x: 0,
            }}
            animate={{
              width: 200,
              height: 48,
              borderRadius: "24px",
              x: -76,
              transition: {
                duration: 0.6,
                ease: "easeInOut",
              },
            }}
            style={{
              ...capsuleStyle,
              position: "absolute",
              top: 0,
              left: 0,
              transformOrigin: "right center",
            }}
          >
            <motion.span
              initial={{ opacity: 0, x: 20 }}
              animate={{
                opacity: 1,
                x: 0,
                transition: {
                  delay: 0.4,
                  duration: 0.4,
                  ease: "easeOut",
                },
              }}
              style={textStyle}
            >
              欢迎使用我们的服务
            </motion.span>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default function App() {
  return <CapsuleAnimation />;
}
