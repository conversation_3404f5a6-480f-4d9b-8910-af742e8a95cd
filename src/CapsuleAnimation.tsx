import { useState, useEffect } from "react";
import { MessageCircle } from "lucide-react";
import "./index.less";

const CapsuleAnimation = () => {
  const [showIcon, setShowIcon] = useState(true);
  const [showCapsule, setShowCapsule] = useState(false);

  useEffect(() => {
    // 2秒后隐藏图标并显示胶囊
    const timer = setTimeout(() => {
      setShowIcon(false);
      setShowCapsule(true);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="capsule-container">
      <div className="animation-wrapper">
        {/* 图标容器 - 始终存在 */}
        <div className="icon-container">
          <div className={`icon ${!showIcon ? "icon-hide" : ""}`}>
            <MessageCircle size={24} color="white" />
          </div>
        </div>

        {/* 胶囊动画 */}
        {showCapsule && (
          <div className="capsule">
            <span className="capsule-text">欢迎使用我们的服务</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default CapsuleAnimation;
